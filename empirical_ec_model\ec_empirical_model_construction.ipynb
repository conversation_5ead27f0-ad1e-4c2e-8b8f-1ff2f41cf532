{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Experiment Description. \n", "\n", "The following five salts were chosen:\n", "\n", "* CN = Calcium Ammonium Nitrate (ag grade)\n", "* AS = Ammonium sulfate\n", "* MKP = Monopotassium phosphate\n", "* SOP = Potassium sulfate\n", "* MS  = Magnesium sulfate heptahydrate\n", "\n", "Using grade A volumetric flasks, five concentrated solutions (250mL each) were prepared with the following weights of the salts:\n", "\n", "* CN  = 24.948g\n", "* AS  = 10.048g\n", "* MKP = 9.962g\n", "* SOP = 10.151g\n", "* MS  = 20.076g\n", "\n", "Using aliquots from these different concentrated solutions, 50 different diluted, 250mL solutions were prepared, and their conductivity and temperature values were measured. The volume of the aliquots \n", "(mL) to prepare each solution, plus the conductivity (mS/cm) and temperature (F) readings can be found in the experiments_to_do_conc_results.csv file. The aliquots were drawn using plastic syringes (+/-5%), the solutions were prepared in grade A volumetric flasks."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["R2 VALUE OBTAINED = 0.9961544262051623\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\python37\\lib\\site-packages\\scipy\\stats\\stats.py:1713: FutureWarning: Using a non-tuple sequence for multidimensional indexing is deprecated; use `arr[tuple(seq)]` instead of `arr[seq]`. In the future this will be interpreted as an array index, `arr[np.array(seq)]`, which will result either in an error or a different result.\n", "  return np.add.reduce(sorted[indexer] * weights, axis=axis) / sumval\n"]}, {"data": {"text/plain": ["Text(0.5, 0, 'Real Conductivity (mS/cm)')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x1080 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.linear_model import *\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "#load the data for the model, the file contains volume of each concentrated solution used to prepare 250mL test solutions\n", "#it also contains the conductivity value measured in mS/cm for each solution\n", "df = pd.read_csv(\"experiments_to_do_conc_results.csv\")\n", "df0 = df.dropna()\n", "\n", "#Concentration of 250mL concentrated solutions that were prepared for each salt\n", "#CN = Calcium Ammonium Nitrate (ag grade)\n", "#AS = Ammonium sulfate\n", "#MKP = Monopotassium phosphate\n", "#SOP = Potassium sulfate\n", "#MS  = Magnesium sulfate heptahydrate\n", "\n", "CN_C   = 1000*24.948/250\n", "AS_C   = 1000*10.048/250\n", "MKP_C  = 1000*9.962/250\n", "SOP_C  = 1000*10.151/250\n", "MS_C   = 1000*20.076/250\n", "\n", "#data for each one of the samples is converted to total ppm values (mg/L) for all the macronutrients.\n", "df0[\"Ca\"] = 1000*((df0[\"CN\"]*CN_C)/250)*0.19\n", "df0[\"N_NO3\"] = 1000*((df0[\"CN\"]*CN_C)/250)*0.144\n", "df0[\"N_NH4\"] = 1000*(((df0[\"CN\"]*CN_C)/250)*0.011+((df0[\"AS\"]*AS_C)/250)*0.2120)\n", "df0[\"S\"] = 1000*(((df0[\"AS\"]*AS_C)/250)*0.24267+((df0[\"SOP\"]*SOP_C)/250)*0.1840+((df0[\"MS\"]*MS_C)/250)*0.1301)\n", "df0[\"K\"] = 1000*(((df0[\"MKP\"]*MKP_C)/250)*0.28732+((df0[\"SOP\"]*SOP_C)/250)*0.44873)\n", "df0[\"P\"] = 1000*((df0[\"MKP\"]*MKP_C)/250)*0.22758\n", "df0[\"Mg\"] = 1000*((df0[\"MS\"]*MS_C)/250)*0.0986\n", "\n", "df0 = df0[[\"N_NO3\", \"N_NH4\", \"Ca\", \"S\", \"K\", \"P\", \"Mg\", \"COND\"]]\n", "inputs = [\"N_NO3\", \"N_NH4\", \"Ca\", \"S\", \"K\", \"P\", \"Mg\"]\n", "df0[inputs] = np.round(df0[inputs], 1)\n", "          \n", "#X contains all the ppm values while y contains the conductivity values we want to predict\n", "X = df0.iloc[:,:-1].values\n", "y = df0.iloc[:,-1].values\n", "\n", "#We are going to use a simple linear regression model for the prediction\n", "m = LinearRegression()\n", "m.fit(X, y)\n", "print(\"R2 VALUE OBTAINED = {}\".format(m.score(X, y)))\n", "\n", "#plot showing all \n", "plt.figure(figsize=(8, 6), dpi= 180, facecolor='w', edgecolor='k')\n", "sns.regplot(x=m.predict(X), y=y, color=\"blue\")\n", "plt.ylabel(\"Predicted (mS/cm)\")\n", "plt.xlabel(\"Real Conductivity (mS/cm)\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}}, "nbformat": 4, "nbformat_minor": 2}