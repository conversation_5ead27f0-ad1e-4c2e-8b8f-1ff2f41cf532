gtk-icon-sizes = "gtk-menu=13,13:gtk-small-toolbar=16,16:gtk-large-toolbar=24,24:gtk-dnd=32,32"
gtk-toolbar-icon-size = small-toolbar

# disable images in buttons. i've only seen ugly delphi apps use this feature.
gtk-button-images = 0

# enable/disable images in menus. most "stock" microsoft apps don't use these, except sparingly.
# the office apps use them heavily, though.
gtk-menu-images = 1

# use the win32 button ordering instead of the GNOME HIG one, where applicable
gtk-alternative-button-order = 1

# use the win32 sort indicators direction, as in Explorer
gtk-alternative-sort-arrows = 1

# Windows users don't expect the PC Speaker beeping at them when they backspace in an empty textview and stuff like that
gtk-error-bell = 0

style "msw-default"
{
  GtkWidget::interior-focus = 1
  GtkOptionMenu::indicator-size = { 9, 5 }
  GtkOptionMenu::indicator-spacing = { 7, 5, 2, 2 }
  GtkSpinButton::shadow-type = in

  # <PERSON> and I disagree that these should be themable
  #GtkUIManager::add-tearoffs = 0
  #GtkComboBox::add-tearoffs = 0
  
  GtkComboBox::appears-as-list = 1
  GtkComboBox::focus-on-click = 0
  
  GOComboBox::add_tearoffs = 0

  GtkTreeView::allow-rules = 0
  GtkTreeView::expander-size = 12

  GtkExpander::expander-size = 12

  GtkScrolledWindow::scrollbar_spacing = 1

  GtkSeparatorMenuItem::horizontal-padding = 2

  engine "wimp" 
  {
  }
}
class "*" style "msw-default"

binding "ms-windows-tree-view"
{
  bind "Right" { "expand-collapse-cursor-row" (1,1,0) }
  bind "Left" { "expand-collapse-cursor-row" (1,0,0) }
}

class "GtkTreeView" binding "ms-windows-tree-view"

style "msw-combobox-thickness" = "msw-default"
{
  xthickness = 0 
  ythickness = 0
}

widget_class "*TreeView*ComboBox*" style "msw-combobox-thickness"
widget_class "*ComboBox*GtkFrame*" style "msw-combobox-thickness"
