{ This is an automatically generated lazarus resource file }

LazarusResources.Add('TForm12','FORMDATA',[
  'TPF0'#7'TForm12'#6'Form12'#4'Left'#3#250#3#6'Height'#3#29#1#3'Top'#3#245#0#5
  +'Width'#3'V'#1#11'BorderStyle'#7#12'bsToolWindow'#7'Caption'#6#24'Choose Deg'
  +'ree of Freedom'#12'ClientHeight'#3#29#1#11'ClientWidth'#3'V'#1#9'FormStyle'
  +#7#11'fsStayOnTop'#10'LCLVersion'#6#8'2.0.13.0'#0#6'TLabel'#6'Label1'#4'Left'
  +#2#24#6'Height'#2#15#3'Top'#3#184#0#5'Width'#3#247#0#7'Caption'#6'-Input ele'
  +'ment to be used as degree of freedom'#11'ParentColor'#8#0#0#6'TLabel'#6'Lab'
  +'el2'#4'Left'#2#24#6'Height'#3#152#0#3'Top'#2#24#5'Width'#3'0'#1#8'AutoSize'
  +#8#7'Caption'#12#156#1#0#0'The degree of freedom is simply an element for wh'
  +'ich any solution is satisfactory. This needs to exist in order for the calc'
  +'ulations to be solvable. Think of it as the element the program is able to '
  +'"adjust" to arrive at better values for other elements. Sulfur is usually u'
  +'sed as a degree of freedom as it commonly allows for the best results to be'
  +' achieved since many elements are usually provided as sulfates.'#11'ParentC'
  +'olor'#8#8'WordWrap'#9#0#0#7'TButton'#7'Button1'#4'Left'#2#24#6'Height'#2#25
  +#3'Top'#3#240#0#5'Width'#2'h'#7'Caption'#6#2'OK'#7'OnClick'#7#12'Button1Clic'
  +'k'#8'TabOrder'#2#0#0#0#9'TComboBox'#9'ComboBox1'#4'Left'#2#28#6'Height'#2#23
  +#3'Top'#3#208#0#5'Width'#2'd'#10'ItemHeight'#2#15#9'ItemIndex'#2#0#13'Items.'
  +'Strings'#1#6#1'S'#6#1'P'#6#1'K'#6#8'N (NO3-)'#6#8'N (NH4+)'#6#2'Mg'#6#2'Ca'
  +#6#2'Cu'#6#2'Fe'#6#2'Mo'#6#2'Si'#6#2'Na'#6#2'Cl'#6#2'Mn'#6#1'B'#6#2'Zn'#0#5
  +'Style'#7#14'csDropDownList'#8'TabOrder'#2#1#4'Text'#6#1'S'#0#0#0
]);
