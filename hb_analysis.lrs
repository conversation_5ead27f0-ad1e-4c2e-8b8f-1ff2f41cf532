{ This is an automatically generated lazarus resource file }

LazarusResources.Add('TForm11','FORMDATA',[
  'TPF0'#7'TForm11'#6'Form11'#4'Left'#3#28#1#6'Height'#3#184#1#3'Top'#3#158#1#5
  +'Width'#3#10#1#11'BorderStyle'#7#12'bsToolWindow'#7'Caption'#6#13'Data Analy'
  +'sis'#12'ClientHeight'#3#184#1#11'ClientWidth'#3#10#1#9'FormStyle'#7#11'fsSt'
  +'ayOnTop'#8'Position'#7#16'poMainFormCenter'#10'LCLVersion'#6#8'2.0.13.0'#0#7
  +'TButton'#7'Button1'#4'Left'#3#181#0#6'Height'#2#25#3'Top'#3#152#1#5'Width'#2
  +'K'#7'Caption'#6#6'Close '#7'OnClick'#7#12'Button1Click'#8'TabOrder'#2#0#0#0
  +#11'TStringGrid'#11'StringGrid1'#4'Left'#2#16#6'Height'#3#128#1#3'Top'#2#16#5
  +'Width'#3#240#0#8'ColCount'#2#2#7'Columns'#14#1#9'Alignment'#7#8'taCenter'#15
  +'Title.Alignment'#7#8'taCenter'#13'Title.Caption'#6#19'Composition % (W/W)'#5
  +'Width'#3#150#0#0#0#15'DefaultColWidth'#3#180#0#8'RowCount'#2#17#8'TabOrder'
  +#2#1#9'ColWidths'#1#2'K'#3#150#0#0#5'Cells'#1#2#17#2#0#2#0#6#7'Element'#2#0#2
  +#1#6#8'N (NO3-)'#2#0#2#2#6#8'N (NH4+)'#2#0#2#3#6#4'P2O5'#2#0#2#4#6#3'K2O'#2#0
  +#2#5#6#2'Mg'#2#0#2#6#6#2'Ca'#2#0#2#7#6#1'S'#2#0#2#8#6#2'Fe'#2#0#2#9#6#2'Mn'#2
  +#0#2#10#6#2'Zn'#2#0#2#11#6#1'B'#2#0#2#12#6#2'Cu'#2#0#2#13#6#2'Si'#2#0#2#14#6
  +#2'Mo'#2#0#2#15#6#2'Na'#2#0#2#16#6#2'Cl'#0#0#0#0
]);
