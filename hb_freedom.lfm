object Form12: TForm12
  Left = 1018
  Height = 285
  Top = 245
  Width = 342
  BorderStyle = bsToolWindow
  Caption = 'Choose Degree of Freedom'
  ClientHeight = 285
  ClientWidth = 342
  FormStyle = fsStayOnTop
  LCLVersion = '2.0.13.0'
  object Label1: TLabel
    Left = 24
    Height = 15
    Top = 184
    Width = 247
    Caption = 'Input element to be used as degree of freedom'
    ParentColor = False
  end
  object Label2: TLabel
    Left = 24
    Height = 152
    Top = 24
    Width = 304
    AutoSize = False
    Caption = 'The degree of freedom is simply an element for which any solution is satisfactory. This needs to exist in order for the calculations to be solvable. Think of it as the element the program is able to "adjust" to arrive at better values for other elements. Sulfur is usually used as a degree of freedom as it commonly allows for the best results to be achieved since many elements are usually provided as sulfates.'
    ParentColor = False
    WordWrap = True
  end
  object Button1: TButton
    Left = 24
    Height = 25
    Top = 240
    Width = 104
    Caption = 'OK'
    OnClick = Button1Click
    TabOrder = 0
  end
  object ComboBox1: TComboBox
    Left = 28
    Height = 23
    Top = 208
    Width = 100
    ItemHeight = 15
    ItemIndex = 0
    Items.Strings = (
      'S'
      'P'
      'K'
      'N (NO3-)'
      'N (NH4+)'
      'Mg'
      'Ca'
      'Cu'
      'Fe'
      'Mo'
      'Si'
      'Na'
      'Cl'
      'Mn'
      'B'
      'Zn'
    )
    Style = csDropDownList
    TabOrder = 1
    Text = 'S'
  end
end
