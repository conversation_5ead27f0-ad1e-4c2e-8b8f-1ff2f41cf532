{ This is an automatically generated lazarus resource file }

LazarusResources.Add('TForm9','FORMDATA',[
  'TPF0'#6'TForm9'#5'Form9'#4'Left'#3#216#0#6'Height'#3#223#1#3'Top'#3#169#1#5
  +'Width'#3#19#2#18'HorzScrollBar.Page'#3#19#2#19'HorzScrollBar.Range'#3#233#1
  +#18'VertScrollBar.Page'#3#224#1#19'VertScrollBar.Range'#3#193#1#11'BorderSty'
  +'le'#7#12'bsToolWindow'#7'Caption'#6'&Detailed Element Contribution Analysis'
  +#12'ClientHeight'#3#223#1#11'ClientWidth'#3#19#2#9'FormStyle'#7#11'fsStayOnT'
  +'op'#10'LCLVersion'#6#8'2.0.13.0'#0#7'TButton'#7'Button1'#4'Left'#3#136#1#6
  +'Height'#2#25#3'Top'#3#168#1#5'Width'#2'`'#7'Caption'#6#5'Close'#7'OnClick'#7
  +#12'Button1Click'#8'TabOrder'#2#0#0#0#11'TStringGrid'#11'StringGrid1'#4'Left'
  +#2'('#6'Height'#3'h'#1#3'Top'#2'('#5'Width'#3#192#1#11'AutoAdvance'#7#6'aaNo'
  +'ne'#8'ColCount'#2#3#7'Columns'#14#1#13'Title.Caption'#6#14'Substance Name'#0
  +#1#9'Alignment'#7#8'taCenter'#15'Title.Alignment'#7#8'taCenter'#13'Title.Cap'
  +'tion'#6#7'Element'#0#1#9'Alignment'#7#8'taCenter'#15'Title.Alignment'#7#8't'
  +'aCenter'#13'Title.Caption'#6#18'Contribution (ppm)'#0#0#20'Constraints.MaxW'
  +'idth'#3#192#1#15'DefaultColWidth'#3#145#0#16'DefaultRowHeight'#2#18#9'Fixed'
  +'Cols'#2#0#8'RowCount'#2#1#10'ScrollBars'#7#14'ssAutoVertical'#8'TabOrder'#2
  +#1#0#0#0
]);
